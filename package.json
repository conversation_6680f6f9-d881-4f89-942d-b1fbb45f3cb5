{"name": "nativemind-extension", "version": "1.3.0-beta.4", "private": false, "author": "NativeMind", "keywords": ["browser-extension", "ai-assistant", "local-ai", "summarization", "web-extension", "native-mind"], "license": "AGPL-3.0-only", "description": "NativeMind – Local AI Assistant for Browsing & Summarizing", "repository": {"type": "git", "url": "https://github.com/NativeMindBrowser/NativeMindExtension"}, "bugs": {"url": "https://github.com/NativeMindBrowser/NativeMindExtension/issues"}, "homepage": "https://github.com/NativeMindBrowser/NativeMindExtension#readme", "sideEffects": ["./utils/**/*"], "type": "module", "scripts": {"build:beta": "wxt build -c wxt.config.beta.ts", "build:prod": "wxt build", "check": "vue-tsc --noEmit && eslint", "lint": "eslint", "lint:fix": "eslint --fix", "compile": "vue-tsc --noEmit", "test:e2e": "playwright test --config=playwright.config.ts", "test:unit": "vitest run", "dev": "wxt", "dev:edge": "wxt -b edge", "dev:firefox": "wxt -b firefox --mv3", "postinstall": "wxt prepare", "release:beta": "release-it --preRelease=beta --config .release-it.beta.json", "release:prod": "release-it", "zip:beta": "wxt zip -c wxt.config.beta.ts", "zip:beta:edge": "wxt zip -c wxt.config.beta.ts -b edge", "zip:beta:firefox": "wxt zip -c wxt.config.beta.ts -b firefox --mv3", "zip:prod": "wxt zip", "zip:prod:firefox": "wxt zip -b firefox --mv3", "prepare": "husky"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@ai-sdk/provider": "^1.1.3", "@ai-sdk/provider-utils": "^2.2.8", "@huggingface/transformers": "^3.5.2", "@mlc-ai/web-llm": "^0.2.79", "@mozilla/readability": "^0.6.0", "@tailwindcss/vite": "^4.1.7", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vueuse/core": "^13.1.0", "ai": "^4.3.13", "best-effort-json-parser": "^1.1.3", "birpc": "^2.3.0", "cheerio": "^1.0.0", "chrome-ai": "^1.11.1", "csstype": "^3.1.3", "dayjs": "^1.11.13", "dompurify": "^3.2.5", "eslint-plugin-vue": "^10.1.0", "events": "^3.3.0", "guesslanguage": "^0.2.0", "highlight.js": "^11.11.1", "katex": "^0.16.22", "lru-cache": "^11.1.0", "marked": "^15.0.11", "marked-directive": "^1.0.7", "marked-highlight": "^2.2.1", "marked-katex-extension": "^5.1.4", "md5": "^2.3.0", "ollama": "^0.5.15", "ollama-ai-provider": "^1.2.0", "pinia": "^3.0.2", "prettier-plugin-packagejson": "^2.5.11", "radash": "^12.1.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "temporal-polyfill": "^0.3.0", "vue": "^3.5.13", "vue-component-type-helpers": "^2.2.10", "vue-i18n": "^11.1.5", "vue-shadow-dom": "^4.2.0", "zod": "^3.24.4"}, "devDependencies": {"@babel/plugin-proposal-explicit-resource-management": "^7.27.1", "@eslint/js": "^9.26.0", "@playwright/test": "^1.53.1", "@stylistic/eslint-plugin": "^4.2.0", "@types/dom-chromium-ai": "^0.0.7", "@types/fs-extra": "^11.0.4", "@types/md5": "^2.3.5", "@types/node": "^24.0.4", "@typescript-eslint/utils": "^8.34.0", "@vitejs/plugin-vue": "^6.0.0", "@vitest/ui": "^3.2.4", "@vue/test-utils": "^2.4.6", "@webgpu/types": "^0.1.60", "@wxt-dev/module-vue": "^1.0.2", "defu": "^6.1.4", "eslint": "^9.26.0", "eslint-plugin-simple-import-sort": "^12.1.1", "fs-extra": "^11.3.0", "glob": "^11.0.3", "globals": "^16.1.0", "happy-dom": "^18.0.1", "husky": "^9.1.7", "prettier-plugin-tailwindcss": "^0.6.11", "release-it": "^19.0.2", "release-it-changelogen": "^0.1.0", "sass-embedded": "^1.87.0", "sharp": "^0.34.2", "typescript": "5.6.3", "typescript-eslint": "^8.32.0", "vite-plugin-webfont-dl": "^3.10.5", "vite-svg-loader": "^5.1.0", "vitest": "^3.2.4", "vue-tsc": "^2.2.10", "wxt": "^0.20.6"}, "packageManager": "pnpm@10.10.0", "engines": {"node": "22.14.0"}}