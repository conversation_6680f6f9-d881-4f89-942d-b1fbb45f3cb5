# Changelog


## v1.3.0-beta.4

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.3.0-beta.3...v1.3.0-beta.4)

### 🚀 Enhancements

- **readme:** Add UTM source parameter to website links ([56a8356](https://github.com/NativeMindBrowser/NativeMindExtension/commit/56a8356))
- **test:** Playwright tests for action button functionality ([d9aa863](https://github.com/NativeMindBrowser/NativeMindExtension/commit/d9aa863))
- **tests:** Add vitest for unit test ([134a8e3](https://github.com/NativeMindBrowser/NativeMindExtension/commit/134a8e3))
- **tests:** Update unit test workflow to output results to summary ([1a3ddf7](https://github.com/NativeMindBrowser/NativeMindExtension/commit/1a3ddf7))
- **workflow:** Include .nvmrc in security scan paths ([9e48ecf](https://github.com/NativeMindBrowser/NativeMindExtension/commit/9e48ecf))
- **workflow:** Redirect npm audit and outdated results to summary ([7dc4c5e](https://github.com/NativeMindBrowser/NativeMindExtension/commit/7dc4c5e))
- **workflow:** Create codeql.yml ([1bf0479](https://github.com/NativeMindBrowser/NativeMindExtension/commit/1bf0479))
- **settings:** Add new quick action settings ([ae064ce](https://github.com/NativeMindBrowser/NativeMindExtension/commit/ae064ce))

### 🩹 Fixes

- **security:** Fix vulnerability alerts ([72b3ea6](https://github.com/NativeMindBrowser/NativeMindExtension/commit/72b3ea6))
- **translator:** Regexp for host matching ([f192f31](https://github.com/NativeMindBrowser/NativeMindExtension/commit/f192f31))

### 🏡 Chore

- **release:** Disable firefox build ([7327481](https://github.com/NativeMindBrowser/NativeMindExtension/commit/7327481))
- **tests:** Update test utils ([0ad59b8](https://github.com/NativeMindBrowser/NativeMindExtension/commit/0ad59b8))
- **workflow:** Remove .nvmrc from security scan paths ([a55ca6a](https://github.com/NativeMindBrowser/NativeMindExtension/commit/a55ca6a))
- **workflow:** Remove security scan workflow file ([c56c468](https://github.com/NativeMindBrowser/NativeMindExtension/commit/c56c468))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))
- Xukecheng <<EMAIL>>

## v1.3.0-beta.3

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.3.0-beta.2...v1.3.0-beta.3)

### 🚀 Enhancements

- **textarea:** Update textarea focus style to improve text visibility ([3849741](https://github.com/NativeMindBrowser/NativeMindExtension/commit/3849741))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.3.0-beta.2

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.3.0-beta.1...v1.3.0-beta.2)

### 🩹 Fixes

- **parser:** Update parseDocument to handle shadow dom ([60f4905](https://github.com/NativeMindBrowser/NativeMindExtension/commit/60f4905))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.3.0-beta.1

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.17...v1.3.0-beta.1)

### 🚀 Enhancements

- **readme:** Add ProductHunt launch announcement and update demo ([3ebe603](https://github.com/NativeMindBrowser/NativeMindExtension/commit/3ebe603))

### 🩹 Fixes

- **readme:** Enhance website link formatting for clarity ([ae8384f](https://github.com/NativeMindBrowser/NativeMindExtension/commit/ae8384f))

### 🏡 Chore

- **release:** V1.2.0 ([b1d9aa9](https://github.com/NativeMindBrowser/NativeMindExtension/commit/b1d9aa9))
- **release:** Update version to v1.3.0-beta.0 in changelog and package.json ([312ac64](https://github.com/NativeMindBrowser/NativeMindExtension/commit/312ac64))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))
- Xukecheng <<EMAIL>>

## v1.3.0-beta.0

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.16...v1.2.0-beta.17)

### 🚀 Enhancements

- **readme:** Add ProductHunt launch announcement and update demo ([83bf9e9](https://github.com/NativeMindBrowser/NativeMindExtension/commit/83bf9e9))
- **fonts:** Add Inter and InterDisplay font files and integrate into the project ([fc6749e](https://github.com/NativeMindBrowser/NativeMindExtension/commit/fc6749e))
- **parser:** Add document cleanup logic for document parser ([1a66f38](https://github.com/NativeMindBrowser/NativeMindExtension/commit/1a66f38))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))
- Xukecheng <<EMAIL>>

## v1.2.0

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.16...v1.2.0)

## v1.2.0-beta.16

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.15...v1.2.0-beta.16)

### 🚀 Enhancements

- **background:** Inject content script on installation ([a9b410d](https://github.com/NativeMindBrowser/NativeMindExtension/commit/a9b410d))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.15

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.14...v1.2.0-beta.15)

### 🚀 Enhancements

- **wxt:** Add module to expose web resources and update config ([93c5d03](https://github.com/NativeMindBrowser/NativeMindExtension/commit/93c5d03))

### 🩹 Fixes

- **styles:** Enhance style injection and loading mechanism for shadow DOM ([e947697](https://github.com/NativeMindBrowser/NativeMindExtension/commit/e947697))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.14

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.13...v1.2.0-beta.14)

### 🩹 Fixes

- **translator:** Ensure translation menu updates only when document is visible and improve context menu handling ([2f8ad72](https://github.com/NativeMindBrowser/NativeMindExtension/commit/2f8ad72))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.13

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.12...v1.2.0-beta.13)

### 🩹 Fixes

- **translator:** Prevent connection attempt to Ollama when translation is disabled ([9b7943f](https://github.com/NativeMindBrowser/NativeMindExtension/commit/9b7943f))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.12

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.11...v1.2.0-beta.12)

### 🩹 Fixes

- **popup:** Show reload message in popup on unattached tabs ([3e4f0f8](https://github.com/NativeMindBrowser/NativeMindExtension/commit/3e4f0f8))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.11

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.10...v1.2.0-beta.11)

### 🚀 Enhancements

- **context-menu:** Refactor context menu items and update translation menu on tab activation ([d23dea6](https://github.com/NativeMindBrowser/NativeMindExtension/commit/d23dea6))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.10

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.9...v1.2.0-beta.10)

### 🚀 Enhancements

- **chat:** Send button style ([243eae7](https://github.com/NativeMindBrowser/NativeMindExtension/commit/243eae7))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.9

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.8...v1.2.0-beta.9)

### 🩹 Fixes

- Translation context menu ([9eb62a1](https://github.com/NativeMindBrowser/NativeMindExtension/commit/9eb62a1))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.8

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.7...v1.2.0-beta.8)

### 🏡 Chore

- **release:** Add environment specification for release job ([efc7e4d](https://github.com/NativeMindBrowser/NativeMindExtension/commit/efc7e4d))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.7

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.6...v1.2.0-beta.7)

### 🩹 Fixes

- Show settings if ollama not available when using translation ([9d1bcf5](https://github.com/NativeMindBrowser/NativeMindExtension/commit/9d1bcf5))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.6

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.5...v1.2.0-beta.6)

### 🚀 Enhancements

- **docs:** Add links to official website in README ([85dfca3](https://github.com/NativeMindBrowser/NativeMindExtension/commit/85dfca3))
- **translator:** improve text content formatting in prompts and add skip target selector
- **translator:** Some pages become unresponsive when using the translation ([7d60f69](https://github.com/NativeMindBrowser/NativeMindExtension/commit/7d60f69))

### 🩹 Fixes

- **readme:** Update demo screenshot ([944ae52](https://github.com/NativeMindBrowser/NativeMindExtension/commit/944ae52))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))
- Xukecheng <<EMAIL>>

## v1.2.0-beta.5

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.4...v1.2.0-beta.5)

### 🚀 Enhancements

- **chat:** enhance send button positioning and styling in chat component chore
- **i18n:** Update extension description for clarity and accuracy ([d49c3bc](https://github.com/NativeMindBrowser/NativeMindExtension/commit/d49c3bc))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.4

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.3...v1.2.0-beta.4)

### 🚀 Enhancements

- **style:** Update ModelSelector container position ([1ddfcba](https://github.com/NativeMindBrowser/NativeMindExtension/commit/1ddfcba))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.3

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.2...v1.2.0-beta.3)

### 🩹 Fixes

- Implement URL validation and timeout handling for url like chrome webstore ([8e4726a](https://github.com/NativeMindBrowser/NativeMindExtension/commit/8e4726a))
- Adjust textarea height handling and update tab selection order ([441d2d0](https://github.com/NativeMindBrowser/NativeMindExtension/commit/441d2d0))

### 💅 Refactors

- **auto-imports:** Disable auto imports ([8bb6192](https://github.com/NativeMindBrowser/NativeMindExtension/commit/8bb6192))
- **lint:** Update eslint rules ([eda482f](https://github.com/NativeMindBrowser/NativeMindExtension/commit/eda482f))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.2

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.1...v1.2.0-beta.2)

## v1.2.0-beta.1


### 🚀 Enhancements

- Welcome to NativeMind ([e6f9f30](https://github.com/NativeMindBrowser/NativeMindExtension/commit/e6f9f30))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0

- 🚀 Welcome to NativeMind!
