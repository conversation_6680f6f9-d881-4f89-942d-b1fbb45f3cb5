name: Unit Tests
on:
  push:
    branches: "*"
    paths:
      - "**/*.ts"
      - "**/*.js"
      - "**/*.vue"
  pull_request:
    branches: [main]
    paths:
      - "**/*.ts"
      - "**/*.js"
      - "**/*.vue"

permissions:
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  test:
    name: Unit Tests
    timeout-minutes: 60
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "pnpm"

      - name: Install dependencies
        run: |
          pnpm install --frozen-lockfile

      - name: Run unit tests
        env:
          NO_COLOR: true
        run: |
          pnpm run test:unit >> $GITHUB_STEP_SUMMARY
