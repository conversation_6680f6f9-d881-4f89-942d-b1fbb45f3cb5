<svg width="257" height="73" viewBox="0 0 257 73" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_413_5957)">
    <path d="M0 0H256.152V62.5714C256.152 68.331 251.483 73 245.723 73H10.4286C4.66905 73 0 68.331 0 62.5714V0Z"
      fill="white" />
    <g filter="url(#filter0_d_413_5957)">
      <g clip-path="url(#clip1_413_5957)">
        <rect x="237.25" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="237.25" width="18.25" height="18.25" fill="white" />
        <rect x="237.902" y="0.652344" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="237.087" y="-0.162946" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter1_d_413_5957)">
      <g clip-path="url(#clip2_413_5957)">
        <rect x="237.25" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="237.902" y="-17.5977" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="237.087" y="-18.4129" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter2_d_413_5957)">
      <g clip-path="url(#clip3_413_5957)">
        <rect x="237.25" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="237.25" y="18.25" width="18.25" height="18.25" fill="white" />
        <rect x="237.902" y="18.9023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="237.087" y="18.0871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter3_d_413_5957)">
      <g clip-path="url(#clip4_413_5957)">
        <rect x="219" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="219" width="18.25" height="18.25" fill="white" />
        <rect x="219.652" y="0.652344" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="218.837" y="-0.162946" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter4_d_413_5957)">
      <g clip-path="url(#clip5_413_5957)">
        <rect x="219" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="219.652" y="-17.5977" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="218.837" y="-18.4129" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter5_d_413_5957)">
      <g clip-path="url(#clip6_413_5957)">
        <rect x="200.75" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="200.75" width="18.25" height="18.25" fill="white" />
        <rect x="201.402" y="0.652344" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="200.587" y="-0.162946" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter6_d_413_5957)">
      <g clip-path="url(#clip7_413_5957)">
        <rect x="200.75" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="201.402" y="-17.5977" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="200.587" y="-18.4129" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter7_d_413_5957)">
      <g clip-path="url(#clip8_413_5957)">
        <rect x="200.75" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="200.75" y="18.25" width="18.25" height="18.25" fill="white" />
        <rect x="201.402" y="18.9023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="200.587" y="18.0871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter8_d_413_5957)">
      <g clip-path="url(#clip9_413_5957)">
        <rect x="182.25" y="0.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="182.25" y="0.25" width="18.25" height="18.25" fill="white" />
        <rect x="182.902" y="0.902344" width="18.25" height="18.25" fill="#F3F3F3" />
        <g clip-path="url(#clip10_413_5957)" filter="url(#filter9_d_413_5957)">
          <path
            d="M195.69 7.57324V13.9521C195.69 14.5307 195.221 15 194.643 15H188.357C187.779 15 187.31 14.5307 187.31 13.9521V5.04785C187.31 4.46927 187.779 4 188.357 4H192.287L195.69 7.57324Z"
            fill="white" />
          <mask id="mask0_413_5957" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="182" y="4" width="14"
            height="12">
            <path
              d="M182.595 5.47549C182.595 4.6606 183.256 4 184.071 4L192.399 4C192.596 4 192.785 4.07911 192.924 4.21963L195.662 6.9955C195.798 7.13355 195.875 7.31968 195.875 7.51361L195.875 14.3284C195.875 15.1433 195.214 15.8039 194.399 15.8039L184.071 15.8039C183.256 15.8039 182.595 15.1433 182.595 14.3284L182.595 5.47549Z"
              fill="url(#paint0_linear_413_5957)" />
          </mask>
          <g mask="url(#mask0_413_5957)">
            <g filter="url(#filter10_ddi_413_5957)">
              <path
                d="M192.555 5.99953L192.555 4.36958C192.555 4.16586 192.39 4.00071 192.186 4.00071L193.293 3.63184L194.768 5.10733L196.244 6.58282L195.875 7.68943C195.875 7.51482 195.733 7.37327 195.559 7.37327H193.929C193.17 7.37327 192.555 6.75823 192.555 5.99953Z"
                fill="url(#paint1_linear_413_5957)" />
            </g>
          </g>
        </g>
      </g>
      <rect x="182.087" y="0.0870536" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter11_d_413_5957)">
      <g clip-path="url(#clip11_413_5957)">
        <rect x="182.5" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="183.152" y="-17.5977" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="182.337" y="-18.4129" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter12_d_413_5957)">
      <g clip-path="url(#clip12_413_5957)">
        <rect x="182.5" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="182.5" y="18.25" width="18.25" height="18.25" fill="white" />
        <rect x="183.152" y="18.9023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="182.337" y="18.0871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter13_d_413_5957)">
      <g clip-path="url(#clip13_413_5957)">
        <rect x="164.25" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="164.25" width="18.25" height="18.25" fill="white" />
        <rect x="164.902" y="0.652344" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="164.087" y="-0.162946" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter14_d_413_5957)">
      <g clip-path="url(#clip14_413_5957)">
        <rect x="164.25" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="164.902" y="-17.5977" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="164.087" y="-18.4129" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter15_d_413_5957)">
      <g clip-path="url(#clip15_413_5957)">
        <rect x="164.25" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="164.25" y="18.25" width="18.25" height="18.25" fill="white" />
        <rect x="164.902" y="18.9023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="164.087" y="18.0871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter16_d_413_5957)">
      <g clip-path="url(#clip16_413_5957)">
        <rect x="146" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="146" width="18.25" height="18.25" fill="white" />
        <rect x="146.652" y="0.652344" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="145.837" y="-0.162946" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter17_d_413_5957)">
      <g clip-path="url(#clip17_413_5957)">
        <rect x="146" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="146.652" y="-17.5977" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="145.837" y="-18.4129" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter18_d_413_5957)">
      <g clip-path="url(#clip18_413_5957)">
        <rect x="128.25" y="0.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="128.25" y="0.25" width="18.25" height="18.25" fill="white" />
        <rect x="128.902" y="0.902344" width="18.25" height="18.25" fill="#F3F3F3" />
        <g clip-path="url(#clip19_413_5957)" filter="url(#filter19_d_413_5957)">
          <path
            d="M141.69 7.57324V13.9521C141.69 14.5307 141.221 15 140.643 15H134.357C133.779 15 133.31 14.5307 133.31 13.9521V5.04785C133.31 4.46927 133.779 4 134.357 4H138.287L141.69 7.57324Z"
            fill="white" />
          <mask id="mask1_413_5957" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="128" y="4" width="14"
            height="12">
            <path
              d="M128.595 5.47549C128.595 4.6606 129.256 4 130.071 4L138.399 4C138.596 4 138.785 4.07911 138.924 4.21963L141.662 6.9955C141.798 7.13355 141.875 7.31968 141.875 7.51361L141.875 14.3284C141.875 15.1433 141.214 15.8039 140.399 15.8039L130.071 15.8039C129.256 15.8039 128.595 15.1433 128.595 14.3284L128.595 5.47549Z"
              fill="url(#paint2_linear_413_5957)" />
          </mask>
          <g mask="url(#mask1_413_5957)">
            <g filter="url(#filter20_ddi_413_5957)">
              <path
                d="M138.555 5.99953L138.555 4.36958C138.555 4.16586 138.39 4.00071 138.186 4.00071L139.293 3.63184L140.768 5.10733L142.244 6.58282L141.875 7.68943C141.875 7.51482 141.733 7.37327 141.559 7.37327H139.929C139.17 7.37327 138.555 6.75823 138.555 5.99953Z"
                fill="url(#paint3_linear_413_5957)" />
            </g>
          </g>
        </g>
      </g>
      <rect x="128.087" y="0.0870536" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter21_d_413_5957)">
      <g clip-path="url(#clip20_413_5957)">
        <rect x="127.75" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="128.402" y="-17.5977" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="127.587" y="-18.4129" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter22_d_413_5957)">
      <g clip-path="url(#clip21_413_5957)">
        <rect x="127.75" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="127.75" y="18.25" width="18.25" height="18.25" fill="white" />
        <rect x="128.402" y="18.9023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="127.587" y="18.0871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter23_d_413_5957)">
      <g clip-path="url(#clip22_413_5957)">
        <rect x="109.5" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="109.5" width="18.25" height="18.25" fill="white" />
        <rect x="110.152" y="0.652344" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="109.337" y="-0.162946" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter24_d_413_5957)">
      <g clip-path="url(#clip23_413_5957)">
        <rect x="109.5" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="110.152" y="-17.5977" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="109.337" y="-18.4129" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter25_d_413_5957)">
      <g clip-path="url(#clip24_413_5957)">
        <rect x="109.5" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="109.5" y="18.25" width="18.25" height="18.25" fill="white" />
        <rect x="110.152" y="18.9023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="109.337" y="18.0871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter26_d_413_5957)">
      <g clip-path="url(#clip25_413_5957)">
        <rect x="91.25" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="91.25" width="18.25" height="18.25" fill="white" />
        <rect x="91.9019" y="0.652344" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="91.0871" y="-0.162946" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter27_d_413_5957)">
      <g clip-path="url(#clip26_413_5957)">
        <rect x="91.25" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="91.9019" y="-17.5977" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="91.0871" y="-18.4129" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter28_d_413_5957)">
      <g clip-path="url(#clip27_413_5957)">
        <rect x="91.25" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="91.25" y="18.25" width="18.25" height="18.25" fill="white" />
        <rect x="91.9019" y="18.9023" width="18.25" height="18.25" fill="#F3F3F3" />
        <g clip-path="url(#clip28_413_5957)" filter="url(#filter29_d_413_5957)">
          <path
            d="M104.69 25.5732V31.9521C104.69 32.5307 104.221 33 103.643 33H97.3574C96.7788 33 96.3096 32.5307 96.3096 31.9521V23.0479C96.3096 22.4693 96.7788 22 97.3574 22H101.287L104.69 25.5732Z"
            fill="white" />
          <mask id="mask2_413_5957" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="91" y="22" width="14"
            height="12">
            <path
              d="M91.5953 23.4755C91.5953 22.6606 92.2559 22 93.0708 22H101.399C101.596 22 101.785 22.0791 101.924 22.2196L104.662 24.9955C104.798 25.1336 104.875 25.3197 104.875 25.5136L104.875 32.3284C104.875 33.1433 104.214 33.8039 103.399 33.8039H93.0708C92.2559 33.8039 91.5953 33.1433 91.5953 32.3284L91.5953 23.4755Z"
              fill="url(#paint4_linear_413_5957)" />
          </mask>
          <g mask="url(#mask2_413_5957)">
            <g filter="url(#filter30_ddi_413_5957)">
              <path
                d="M101.555 23.9995L101.555 22.3696C101.555 22.1659 101.39 22.0007 101.186 22.0007L102.293 21.6318L103.768 23.1073L105.244 24.5828L104.875 25.6894C104.875 25.5148 104.733 25.3733 104.559 25.3733H102.929C102.17 25.3733 101.555 24.7582 101.555 23.9995Z"
                fill="url(#paint5_linear_413_5957)" />
            </g>
          </g>
        </g>
      </g>
      <rect x="91.0871" y="18.0871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter31_d_413_5957)">
      <g clip-path="url(#clip29_413_5957)">
        <rect x="73" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="73.6519" y="-17.5977" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="72.8371" y="-18.4129" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter32_d_413_5957)">
      <g clip-path="url(#clip30_413_5957)">
        <rect x="73" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="73" y="18.25" width="18.25" height="18.25" fill="white" />
        <rect x="73.6519" y="18.9023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="72.8371" y="18.0871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter33_d_413_5957)">
      <g clip-path="url(#clip31_413_5957)">
        <rect x="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="54.75" width="18.25" height="18.25" fill="white" />
        <rect x="55.4019" y="0.652344" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="54.5871" y="-0.162946" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter34_d_413_5957)">
      <g clip-path="url(#clip32_413_5957)">
        <rect x="54.75" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="55.4019" y="-17.5977" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="54.5871" y="-18.4129" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter35_d_413_5957)">
      <g clip-path="url(#clip33_413_5957)">
        <rect x="54.75" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="54.75" y="18.25" width="18.25" height="18.25" fill="white" />
        <rect x="55.4019" y="18.9023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="54.5871" y="18.0871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter36_d_413_5957)">
      <g clip-path="url(#clip34_413_5957)">
        <rect x="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="36.5" width="18.25" height="18.25" fill="white" />
        <rect x="37.1519" y="0.652344" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="36.3371" y="-0.162946" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter37_d_413_5957)">
      <g clip-path="url(#clip35_413_5957)">
        <rect x="36.5" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="37.1519" y="-17.5977" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="36.3371" y="-18.4129" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter38_d_413_5957)">
      <g clip-path="url(#clip36_413_5957)">
        <rect x="237.25" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="237.25" y="36.5" width="18.25" height="18.25" fill="white" />
        <rect x="237.902" y="37.1523" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="237.087" y="36.3371" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter39_d_413_5957)">
      <g clip-path="url(#clip37_413_5957)">
        <rect x="237.25" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="237.25" y="54.75" width="18.25" height="18.25" fill="white" />
        <rect x="237.902" y="55.4023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="237.087" y="54.5871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter40_d_413_5957)">
      <g clip-path="url(#clip38_413_5957)">
        <rect x="219" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="219" y="36.5" width="18.25" height="18.25" fill="white" />
        <rect x="219.652" y="37.1523" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="218.837" y="36.3371" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter41_d_413_5957)">
      <g clip-path="url(#clip39_413_5957)">
        <rect x="219" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="219" y="54.75" width="18.25" height="18.25" fill="white" />
        <rect x="219.652" y="55.4023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="218.837" y="54.5871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter42_d_413_5957)">
      <g clip-path="url(#clip40_413_5957)">
        <rect x="201.25" y="36.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="201.25" y="36.25" width="18.25" height="18.25" fill="white" />
        <rect x="201.902" y="36.9023" width="18.25" height="18.25" fill="#F3F3F3" />
        <g clip-path="url(#clip41_413_5957)" filter="url(#filter43_d_413_5957)">
          <path
            d="M214.69 43.5732V49.9521C214.69 50.5307 214.221 51 213.643 51H207.357C206.779 51 206.31 50.5307 206.31 49.9521V41.0479C206.31 40.4693 206.779 40 207.357 40H211.287L214.69 43.5732Z"
            fill="white" />
          <mask id="mask3_413_5957" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="201" y="40" width="14"
            height="12">
            <path
              d="M201.595 41.4755C201.595 40.6606 202.256 40 203.071 40H211.399C211.596 40 211.785 40.0791 211.924 40.2196L214.662 42.9955C214.798 43.1336 214.875 43.3197 214.875 43.5136L214.875 50.3284C214.875 51.1433 214.214 51.8039 213.399 51.8039H203.071C202.256 51.8039 201.595 51.1433 201.595 50.3284L201.595 41.4755Z"
              fill="url(#paint6_linear_413_5957)" />
          </mask>
          <g mask="url(#mask3_413_5957)">
            <g filter="url(#filter44_ddi_413_5957)">
              <path
                d="M211.555 41.9995L211.555 40.3696C211.555 40.1659 211.39 40.0007 211.186 40.0007L212.293 39.6318L213.768 41.1073L215.244 42.5828L214.875 43.6894C214.875 43.5148 214.733 43.3733 214.559 43.3733H212.929C212.17 43.3733 211.555 42.7582 211.555 41.9995Z"
                fill="url(#paint7_linear_413_5957)" />
            </g>
          </g>
        </g>
      </g>
      <rect x="201.087" y="36.0871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter45_d_413_5957)">
      <g clip-path="url(#clip42_413_5957)">
        <rect x="200.75" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="200.75" y="54.75" width="18.25" height="18.25" fill="white" />
        <rect x="201.402" y="55.4023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="200.587" y="54.5871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter46_d_413_5957)">
      <g clip-path="url(#clip43_413_5957)">
        <rect x="182.5" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="182.5" y="36.5" width="18.25" height="18.25" fill="white" />
        <rect x="183.152" y="37.1523" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="182.337" y="36.3371" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter47_d_413_5957)">
      <g clip-path="url(#clip44_413_5957)">
        <rect x="182.5" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="182.5" y="54.75" width="18.25" height="18.25" fill="white" />
        <rect x="183.152" y="55.4023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="182.337" y="54.5871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter48_d_413_5957)">
      <g clip-path="url(#clip45_413_5957)">
        <rect x="164.25" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="164.25" y="36.5" width="18.25" height="18.25" fill="white" />
        <rect x="164.902" y="37.1523" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="164.087" y="36.3371" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter49_d_413_5957)">
      <g clip-path="url(#clip46_413_5957)">
        <rect x="164.25" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="164.25" y="54.75" width="18.25" height="18.25" fill="white" />
        <rect x="164.902" y="55.4023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="164.087" y="54.5871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter50_d_413_5957)">
      <g clip-path="url(#clip47_413_5957)">
        <rect x="146" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="146" y="36.5" width="18.25" height="18.25" fill="white" />
        <rect x="146.652" y="37.1523" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="145.837" y="36.3371" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter51_d_413_5957)">
      <g clip-path="url(#clip48_413_5957)">
        <rect x="146" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="146" y="54.75" width="18.25" height="18.25" fill="white" />
        <rect x="146.652" y="55.4023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="145.837" y="54.5871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter52_d_413_5957)">
      <g clip-path="url(#clip49_413_5957)">
        <rect x="127.75" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="127.75" y="36.5" width="18.25" height="18.25" fill="white" />
        <rect x="128.402" y="37.1523" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="127.587" y="36.3371" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter53_d_413_5957)">
      <g clip-path="url(#clip50_413_5957)">
        <rect x="127.75" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="127.75" y="54.75" width="18.25" height="18.25" fill="white" />
        <rect x="128.402" y="55.4023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="127.587" y="54.5871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter54_d_413_5957)">
      <g clip-path="url(#clip51_413_5957)">
        <rect x="109.5" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="109.5" y="36.5" width="18.25" height="18.25" fill="white" />
        <rect x="110.152" y="37.1523" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="109.337" y="36.3371" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter55_d_413_5957)">
      <g clip-path="url(#clip52_413_5957)">
        <rect x="109.5" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="109.5" y="54.75" width="18.25" height="18.25" fill="white" />
        <rect x="110.152" y="55.4023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="109.337" y="54.5871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter56_d_413_5957)">
      <g clip-path="url(#clip53_413_5957)">
        <rect x="91.25" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="91.25" y="36.5" width="18.25" height="18.25" fill="white" />
        <rect x="91.9019" y="37.1523" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="91.0871" y="36.3371" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter57_d_413_5957)">
      <g clip-path="url(#clip54_413_5957)">
        <rect x="91.25" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="91.2501" y="54.75" width="18.25" height="18.25" fill="white" />
        <rect x="91.9019" y="55.4023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="91.0871" y="54.5871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter58_d_413_5957)">
      <g clip-path="url(#clip55_413_5957)">
        <rect x="73" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="73" y="36.5" width="18.25" height="18.25" fill="white" />
        <rect x="73.6519" y="37.1523" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="72.8371" y="36.3371" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter59_d_413_5957)">
      <g clip-path="url(#clip56_413_5957)">
        <rect x="73" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="73" y="54.75" width="18.25" height="18.25" fill="white" />
        <rect x="73.6519" y="55.4023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="72.8371" y="54.5871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter60_d_413_5957)">
      <g clip-path="url(#clip57_413_5957)">
        <rect x="54.75" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="54.75" y="36.5" width="18.25" height="18.25" fill="white" />
        <rect x="55.4019" y="37.1523" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="54.5871" y="36.3371" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter61_d_413_5957)">
      <g clip-path="url(#clip58_413_5957)">
        <rect x="54.75" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="54.75" y="54.75" width="18.25" height="18.25" fill="white" />
        <rect x="55.4019" y="55.4023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="54.5871" y="54.5871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter62_d_413_5957)">
      <g clip-path="url(#clip59_413_5957)">
        <rect x="36.5" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="36.5" y="36.5" width="18.25" height="18.25" fill="white" />
        <rect x="37.1519" y="37.1523" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="36.3371" y="36.3371" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter63_d_413_5957)">
      <g clip-path="url(#clip60_413_5957)">
        <rect x="36.5" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="36.5" y="54.75" width="18.25" height="18.25" fill="white" />
        <rect x="37.1519" y="55.4023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="36.3371" y="54.5871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter64_d_413_5957)">
      <g clip-path="url(#clip61_413_5957)">
        <rect x="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="18.25" width="18.25" height="18.25" fill="white" />
        <rect x="18.9019" y="0.652344" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="18.0871" y="-0.162946" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter65_d_413_5957)">
      <g clip-path="url(#clip62_413_5957)">
        <rect x="18.25" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="18.9019" y="-17.5977" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="18.0871" y="-18.4129" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter66_d_413_5957)">
      <g clip-path="url(#clip63_413_5957)">
        <rect x="18.25" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="18.25" y="18.25" width="18.25" height="18.25" fill="white" />
        <rect x="18.9019" y="18.9023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="18.0871" y="18.0871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter67_d_413_5957)">
      <g clip-path="url(#clip64_413_5957)">
        <rect width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect width="18.25" height="18.25" fill="white" />
        <rect x="0.651855" y="0.652344" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="-0.162946" y="-0.162946" width="18.5759" height="18.5759" rx="0.814732" stroke="black"
        stroke-opacity="0.2" stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter68_d_413_5957)">
      <g clip-path="url(#clip65_413_5957)">
        <rect y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="0.651855" y="-17.5977" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="-0.162946" y="-18.4129" width="18.5759" height="18.5759" rx="0.814732" stroke="black"
        stroke-opacity="0.2" stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter69_d_413_5957)">
      <g clip-path="url(#clip66_413_5957)">
        <rect y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect y="18.25" width="18.25" height="18.25" fill="white" />
        <rect x="0.651855" y="18.9023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="-0.162946" y="18.0871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter70_d_413_5957)">
      <g clip-path="url(#clip67_413_5957)">
        <rect x="18.25" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white"
          shape-rendering="crispEdges" />
        <rect x="18.25" y="54.75" width="18.25" height="18.25" fill="white" />
        <rect x="18.9019" y="55.4023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="18.0871" y="54.5871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter71_d_413_5957)">
      <g clip-path="url(#clip68_413_5957)">
        <rect y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect y="36.5" width="18.25" height="18.25" fill="white" />
        <rect x="0.651855" y="37.1523" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="-0.162946" y="36.3371" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter72_d_413_5957)">
      <g clip-path="url(#clip69_413_5957)">
        <rect y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect y="54.75" width="18.25" height="18.25" fill="white" />
        <rect x="0.651855" y="55.4023" width="18.25" height="18.25" fill="#F3F3F3" />
      </g>
      <rect x="-0.162946" y="54.5871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter73_d_413_5957)">
      <rect x="48.884" y="41.7148" width="157.969" height="113.13" rx="4.13892" fill="white" />
      <circle cx="56.1745" cy="49.4661" r="2.19464" fill="#D9D9D9" />
      <circle cx="63.4899" cy="49.4642" r="2.19464" fill="#D9D9D9" />
      <circle cx="70.8054" cy="49.4642" r="2.19464" fill="#D9D9D9" />
      <rect x="58.5415" y="63.3389" width="47.5976" height="4.13892" rx="2.06946" fill="#D9D9D9" />
      <rect x="56.6445" y="61.441" width="51.3916" height="7.93293" rx="3.96646" stroke="#F2F2F2"
        stroke-width="0.34491" />
      <rect x="115.107" y="61.2686" width="83.4682" height="20.0411" rx="4.45357" fill="#F2F2F2" />
      <rect x="180.546" y="64.0283" width="14.6904" height="14.4862" rx="1.67009" fill="white" />
      <path
        d="M192.929 50.9131V49.6782C192.929 49.5526 192.965 49.4523 193.038 49.3772C193.113 49.3022 193.205 49.2647 193.314 49.2647C193.374 49.2647 193.432 49.2769 193.488 49.3013C193.547 49.3257 193.603 49.3613 193.657 49.4082C193.723 49.4663 193.792 49.5132 193.865 49.5488C193.939 49.5826 194.016 49.5995 194.099 49.5995C194.251 49.5995 194.381 49.5488 194.49 49.4476C194.599 49.3444 194.653 49.2075 194.653 49.0369C194.653 48.8662 194.599 48.7293 194.49 48.6262C194.381 48.523 194.251 48.4715 194.099 48.4715C194.016 48.4715 193.939 48.4893 193.865 48.5249C193.792 48.5587 193.723 48.6046 193.657 48.6628C193.603 48.7096 193.547 48.7453 193.488 48.7696C193.432 48.794 193.374 48.8062 193.314 48.8062C193.205 48.8062 193.113 48.7696 193.038 48.6965C192.965 48.6215 192.929 48.5202 192.929 48.3927V47.1522C192.929 46.869 193.006 46.6524 193.159 46.5024C193.313 46.3524 193.534 46.2773 193.82 46.2773H197.289C197.576 46.2773 197.795 46.3524 197.947 46.5024C198.101 46.6524 198.178 46.869 198.178 47.1522V48.1114C198.178 48.1339 198.185 48.1499 198.2 48.1592C198.215 48.1667 198.237 48.162 198.265 48.1452C198.319 48.1077 198.377 48.0748 198.437 48.0467C198.498 48.0167 198.563 47.9942 198.631 47.9792C198.7 47.9623 198.772 47.9539 198.847 47.9539C199.027 47.9539 199.197 47.9989 199.356 48.0889C199.516 48.1789 199.644 48.3055 199.742 48.4687C199.839 48.6318 199.888 48.8222 199.888 49.0397C199.888 49.2591 199.839 49.4504 199.742 49.6135C199.644 49.7767 199.516 49.9042 199.356 49.9961C199.197 50.0861 199.027 50.1311 198.847 50.1311C198.772 50.1311 198.7 50.1227 198.631 50.1058C198.563 50.0889 198.498 50.0664 198.437 50.0383C198.377 50.0102 198.319 49.9774 198.265 49.9398C198.237 49.9211 198.215 49.9164 198.2 49.9258C198.185 49.9333 198.178 49.9483 198.178 49.9708V50.9131C198.178 51.1963 198.101 51.4129 197.947 51.5629C197.795 51.7148 197.576 51.7908 197.289 51.7908H193.82C193.534 51.7908 193.313 51.7148 193.159 51.5629C193.006 51.4129 192.929 51.1963 192.929 50.9131ZM193.458 50.8878C193.458 51.0078 193.49 51.0997 193.556 51.1635C193.622 51.2273 193.713 51.2591 193.829 51.2591H197.28C197.397 51.2591 197.487 51.2273 197.55 51.1635C197.616 51.0997 197.649 51.0078 197.649 50.8878V49.6923C197.649 49.5648 197.685 49.4626 197.759 49.3857C197.834 49.3088 197.926 49.2704 198.034 49.2704C198.096 49.2704 198.155 49.2825 198.211 49.3069C198.268 49.3313 198.323 49.3669 198.377 49.4138C198.445 49.472 198.515 49.5188 198.588 49.5545C198.662 49.5901 198.738 49.6079 198.819 49.6079C198.971 49.6079 199.101 49.5563 199.21 49.4532C199.319 49.3482 199.373 49.2103 199.373 49.0397C199.373 48.8709 199.319 48.735 199.21 48.6318C199.101 48.5287 198.971 48.4771 198.819 48.4771C198.738 48.4771 198.662 48.4949 198.588 48.5305C198.515 48.5662 198.445 48.6121 198.377 48.6684C198.323 48.7171 198.268 48.7537 198.211 48.7781C198.155 48.8025 198.096 48.8147 198.034 48.8147C197.926 48.8147 197.834 48.7762 197.759 48.6993C197.685 48.6224 197.649 48.5202 197.649 48.3927V47.1775C197.649 47.0594 197.616 46.9684 197.55 46.9046C197.487 46.839 197.397 46.8062 197.28 46.8062H193.829C193.713 46.8062 193.622 46.839 193.556 46.9046C193.49 46.9684 193.458 47.0594 193.458 47.1775V48.072C193.458 48.132 193.47 48.1592 193.494 48.1536C193.52 48.1461 193.547 48.1348 193.573 48.1198C193.627 48.0861 193.684 48.0561 193.742 48.0298C193.8 48.0036 193.861 47.9839 193.925 47.9708C193.988 47.9558 194.055 47.9483 194.124 47.9483C194.306 47.9483 194.476 47.9933 194.633 48.0833C194.793 48.1733 194.921 48.2999 195.019 48.463C195.118 48.6262 195.168 48.8175 195.168 49.0369C195.168 49.2544 195.118 49.4448 195.019 49.6079C194.921 49.7711 194.793 49.8977 194.633 49.9877C194.476 50.0777 194.306 50.1227 194.124 50.1227C194.055 50.1227 193.988 50.1152 193.925 50.1002C193.861 50.0852 193.8 50.0655 193.742 50.0411C193.684 50.0149 193.627 49.9858 193.573 49.9539C193.547 49.937 193.52 49.9258 193.494 49.9202C193.47 49.9145 193.458 49.9408 193.458 49.9989V50.8878Z"
        fill="#D9D9D9" />
    </g>
    <g filter="url(#filter74_d_413_5957)">
      <g clip-path="url(#clip70_413_5957)">
        <rect x="219" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="219" y="18.25" width="18.25" height="18.25" fill="white" />
        <path
          d="M218.348 27.3744C218.348 21.9749 222.725 17.5977 228.125 17.5977C233.524 17.5977 237.902 21.9749 237.902 27.3744C237.902 32.774 233.524 37.1512 228.125 37.1512C222.725 37.1512 218.348 32.774 218.348 27.3744Z"
          fill="white" />
        <g clip-path="url(#clip71_413_5957)">
          <path
            d="M225.699 23.5732H225.688L225.673 24.816H225.684C226.499 24.816 227.131 25.461 228.507 27.7845L228.59 27.9257L228.596 27.9352L229.366 26.7765L229.36 26.7675C229.192 26.492 229.018 26.2201 228.838 25.9519C228.661 25.6873 228.475 25.429 228.28 25.1777C227.37 24.0162 226.61 23.5732 225.699 23.5732Z"
            fill="url(#paint8_linear_413_5957)" />
          <path
            d="M225.688 23.5732C224.774 23.578 223.965 24.1711 223.382 25.0798C223.38 25.0825 223.379 25.0852 223.377 25.0879L224.448 25.6729L224.453 25.6649C224.795 25.1502 225.219 24.8218 225.674 24.8165H225.684L225.699 23.5732H225.688Z"
            fill="url(#paint9_linear_413_5957)" />
          <path
            d="M223.381 25.0781L223.376 25.0862C222.992 25.685 222.706 26.4207 222.552 27.2139L222.55 27.2244L223.754 27.5096L223.756 27.4991C223.884 26.8019 224.129 26.1551 224.448 25.6717L224.453 25.6636L223.381 25.0781Z"
            fill="url(#paint10_linear_413_5957)" />
          <path
            d="M223.756 27.5L222.552 27.2148L222.55 27.2253C222.465 27.6616 222.423 28.105 222.422 28.5494V28.5603L223.656 28.671V28.6601C223.652 28.2712 223.685 27.8829 223.756 27.5005L223.756 27.5Z"
            fill="url(#paint11_linear_413_5957)" />
          <path
            d="M223.694 29.0549C223.672 28.9273 223.659 28.7981 223.656 28.6685V28.6581L222.422 28.5469V28.5583C222.42 28.8216 222.443 29.0844 222.491 29.3434L223.696 29.0654C223.695 29.0619 223.694 29.0584 223.694 29.0549Z"
            fill="url(#paint12_linear_413_5957)" />
          <path
            d="M223.976 29.6987C223.841 29.5514 223.746 29.3394 223.696 29.0676L223.694 29.0576L222.489 29.3356L222.491 29.3456C222.582 29.8256 222.761 30.2249 223.017 30.5276L223.023 30.5357L223.982 29.7063C223.98 29.7038 223.978 29.7013 223.976 29.6987Z"
            fill="url(#paint13_linear_413_5957)" />
          <path
            d="M227.545 26.2598C226.819 27.3766 226.379 28.0776 226.379 28.0776C225.411 29.5985 225.077 29.9392 224.539 29.9392C224.433 29.942 224.328 29.9218 224.231 29.8802C224.133 29.8385 224.046 29.7763 223.975 29.6978L223.017 30.5267L223.023 30.5347C223.377 30.9477 223.875 31.1768 224.492 31.1768C225.425 31.1768 226.095 30.7358 227.288 28.6437L228.128 27.1561C227.941 26.8521 227.747 26.5532 227.545 26.2598Z"
            fill="#0082FB" />
          <path
            d="M228.838 24.4966L228.831 24.5042C228.641 24.7085 228.457 24.9357 228.28 25.1771C228.459 25.4067 228.645 25.6638 228.838 25.9518C229.066 25.5987 229.279 25.3126 229.488 25.093L229.495 25.0854L228.838 24.4966Z"
            fill="url(#paint14_linear_413_5957)" />
          <path
            d="M232.363 24.3874C231.857 23.8741 231.253 23.5732 230.608 23.5732C229.927 23.5732 229.355 23.9473 228.839 24.4971L228.831 24.5048L229.488 25.0941L229.495 25.086C229.835 24.731 230.165 24.5537 230.53 24.5537C230.922 24.5537 231.29 24.7391 231.608 25.0646L231.615 25.0722L232.371 24.395L232.363 24.3874Z"
            fill="#0082FB" />
          <path
            d="M233.827 28.3846C233.798 26.7369 233.223 25.264 232.371 24.3943L232.363 24.3867L231.608 25.0635L231.616 25.0711C232.257 25.7327 232.698 26.9626 232.738 28.3841V28.3951H233.827V28.3846Z"
            fill="url(#paint15_linear_413_5957)" />
          <path
            d="M233.827 28.3957V28.3848H232.738V28.3952C232.74 28.4618 232.741 28.5292 232.741 28.5967C232.741 28.9841 232.683 29.2973 232.566 29.5235L232.56 29.5339L233.372 30.3809L233.378 30.3714C233.673 29.9151 233.828 29.2816 233.828 28.5131C233.828 28.4736 233.828 28.4347 233.827 28.3957Z"
            fill="url(#paint16_linear_413_5957)" />
          <path
            d="M232.565 29.5215L232.56 29.531C232.458 29.722 232.313 29.8494 232.124 29.905L232.494 31.0751C232.565 31.051 232.635 31.0221 232.702 30.9886C232.965 30.8556 233.189 30.656 233.351 30.4097L233.372 30.3789L233.378 30.3693L232.565 29.5215Z"
            fill="url(#paint17_linear_413_5957)" />
          <path
            d="M231.889 29.9367C231.764 29.9367 231.655 29.9181 231.548 29.8701L231.168 31.0687C231.382 31.1414 231.609 31.1742 231.862 31.1742C232.096 31.1742 232.311 31.1395 232.505 31.0721L232.134 29.902C232.055 29.9257 231.973 29.9376 231.889 29.9367Z"
            fill="url(#paint18_linear_413_5957)" />
          <path
            d="M231.13 29.5276L231.123 29.5195L230.25 30.4292L230.258 30.4373C230.561 30.7614 230.85 30.9624 231.179 31.0727L231.557 29.875C231.419 29.8156 231.285 29.7073 231.13 29.5276Z"
            fill="url(#paint19_linear_413_5957)" />
          <path
            d="M231.123 29.5193C230.862 29.2141 230.538 28.7056 230.029 27.8844L229.365 26.7742L229.36 26.7646L228.59 27.9233L228.596 27.9328L229.066 28.7256C229.522 29.4907 229.893 30.0439 230.251 30.4294L230.258 30.437L231.13 29.5273C231.128 29.5247 231.126 29.522 231.123 29.5193Z"
            fill="url(#paint20_linear_413_5957)" />
        </g>
      </g>
      <rect x="218.837" y="18.0871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter75_d_413_5957)">
      <g clip-path="url(#clip72_413_5957)">
        <rect x="73" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="73" width="18.25" height="18.25" fill="white" />
        <path
          d="M72.3483 9.12444C72.3483 3.72487 76.7255 -0.652344 82.1251 -0.652344C87.5246 -0.652344 91.9018 3.72487 91.9018 9.12444C91.9018 14.524 87.5246 18.9012 82.1251 18.9012C76.7255 18.9012 72.3483 14.524 72.3483 9.12444Z"
          fill="white" fill-opacity="0.94" />
        <g clip-path="url(#clip73_413_5957)">
          <path
            d="M87.7083 5.55188C87.5876 5.49294 87.5353 5.60558 87.465 5.66309C87.4408 5.68162 87.4203 5.70586 87.3999 5.72772C87.2231 5.9164 87.0168 6.03997 86.7474 6.02524C86.3534 6.00337 86.0169 6.12694 85.7194 6.42826C85.6562 6.0566 85.4461 5.83513 85.1267 5.69255C84.9594 5.61841 84.7902 5.54475 84.6728 5.38363C84.5911 5.2691 84.5688 5.14125 84.5279 5.01578C84.5018 4.93974 84.4756 4.86227 84.3886 4.84944C84.2936 4.83471 84.2565 4.91408 84.2194 4.98061C84.0707 5.25246 84.0132 5.55188 84.0189 5.85509C84.0317 6.53757 84.3197 7.08126 84.8924 7.46765C84.9575 7.51185 84.9742 7.55652 84.9537 7.62116C84.9148 7.75423 84.8682 7.8835 84.8273 8.01705C84.8012 8.10212 84.7622 8.12018 84.6709 8.08359C84.3626 7.95108 84.0826 7.76071 83.8459 7.52278C83.4386 7.12927 83.0707 6.69488 82.6116 6.35459C82.5053 6.27599 82.396 6.20132 82.2842 6.13074C81.8161 5.67592 82.346 5.30236 82.4686 5.25817C82.5969 5.21159 82.5128 5.05285 82.0984 5.05475C81.6839 5.05665 81.3047 5.19496 80.8213 5.37983C80.7495 5.40735 80.6756 5.42913 80.6003 5.44494C80.1487 5.35984 79.6867 5.3435 79.2302 5.39647C78.3343 5.49627 77.619 5.9202 77.0929 6.64307C76.4608 7.51185 76.3121 8.49944 76.4941 9.52885C76.6856 10.6143 77.2398 11.5131 78.091 12.2155C78.974 12.9441 79.9906 13.301 81.1507 13.2326C81.855 13.1922 82.6397 13.0976 83.5241 12.3486C83.7475 12.4598 83.9813 12.504 84.3701 12.5373C84.6695 12.5653 84.9575 12.523 85.1804 12.4764C85.5297 12.4023 85.5055 12.0786 85.3796 12.0197C84.3554 11.5425 84.5802 11.7369 84.3753 11.5796C84.8962 10.9637 85.6804 10.324 85.9874 8.25135C86.0112 8.08644 85.9907 7.98283 85.9874 7.84976C85.9855 7.76897 86.0041 7.73712 86.0967 7.72809C86.3539 7.70142 86.6033 7.62473 86.831 7.50234C87.4945 7.13972 87.7625 6.54469 87.8257 5.83085C87.8352 5.72154 87.8238 5.60938 87.7083 5.55188ZM81.9258 11.9764C80.933 11.1961 80.4516 10.939 80.2529 10.9499C80.0666 10.9613 80.1004 11.1737 80.1412 11.3125C80.184 11.4494 80.2396 11.5435 80.3176 11.6637C80.3717 11.7431 80.4088 11.8614 80.2639 11.9503C79.944 12.148 79.3884 11.8838 79.3623 11.8709C78.7155 11.4898 78.1741 10.987 77.7935 10.2993C77.4256 9.63721 77.2117 8.92718 77.1766 8.16913C77.1671 7.98568 77.2208 7.92105 77.4033 7.88778C77.6428 7.84193 77.8883 7.83567 78.1299 7.86925C79.1432 8.01753 80.0053 8.47045 80.7287 9.18762C81.1412 9.59634 81.4534 10.0844 81.7752 10.5616C82.1174 11.0682 82.4852 11.5511 82.9538 11.9465C83.1192 12.0853 83.2509 12.1908 83.3773 12.2683C82.9961 12.311 82.3602 12.3205 81.9258 11.9764ZM82.4011 8.91577C82.401 8.89216 82.4067 8.86889 82.4176 8.84796C82.4285 8.82703 82.4444 8.80907 82.4638 8.79565C82.4832 8.78222 82.5056 8.77372 82.5291 8.77088C82.5525 8.76805 82.5763 8.77096 82.5983 8.77937C82.6265 8.78946 82.6507 8.80806 82.6678 8.8326C82.6848 8.85713 82.6938 8.88637 82.6934 8.91624C82.6935 8.93554 82.6897 8.95466 82.6823 8.97249C82.6749 8.99031 82.664 9.00648 82.6503 9.02006C82.6366 9.03365 82.6203 9.04436 82.6024 9.05159C82.5845 9.05881 82.5654 9.0624 82.5461 9.06215C82.5269 9.06221 82.5079 9.05846 82.4902 9.0511C82.4726 9.04375 82.4565 9.03294 82.4431 9.0193C82.4296 9.00567 82.419 8.9895 82.4119 8.97172C82.4048 8.95394 82.4008 8.93492 82.4011 8.91577ZM83.8792 9.67428C83.7841 9.71278 83.6895 9.74605 83.5988 9.75033C83.4622 9.75509 83.3282 9.71243 83.2195 9.62961C83.0893 9.5203 82.9961 9.45947 82.9572 9.26936C82.9437 9.17646 82.9463 9.08194 82.9648 8.98991C82.998 8.8345 82.961 8.73469 82.8512 8.6444C82.7623 8.57025 82.6487 8.54982 82.5242 8.54982C82.4816 8.54734 82.4402 8.53463 82.4035 8.51275C82.3512 8.48708 82.3084 8.42245 82.3493 8.3426C82.3626 8.31694 82.4253 8.25421 82.4405 8.2428C82.6097 8.1468 82.8051 8.17816 82.9852 8.2504C83.1525 8.31884 83.2789 8.44431 83.4609 8.62206C83.6468 8.8364 83.6805 8.89581 83.7865 9.05645C83.8701 9.18239 83.9462 9.31166 83.998 9.45947C84.0298 9.55214 83.9889 9.62771 83.8792 9.67428Z"
            fill="#4D6BFE" />
        </g>
      </g>
      <rect x="72.8371" y="-0.162946" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter76_d_413_5957)">
      <g clip-path="url(#clip74_413_5957)">
        <rect x="146" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="146" y="18.25" width="18.25" height="18.25" fill="white" />
        <path
          d="M145.348 27.3744C145.348 21.9749 149.725 17.5977 155.125 17.5977C160.524 17.5977 164.902 21.9749 164.902 27.3744C164.902 32.774 160.524 37.1512 155.125 37.1512C149.725 37.1512 145.348 32.774 145.348 27.3744Z"
          fill="white" fill-opacity="0.94" />
        <g clip-path="url(#clip75_413_5957)">
          <path
            d="M155.412 22.3082C155.599 22.6361 155.784 22.965 155.97 23.2943C155.977 23.3075 155.988 23.3184 156.001 23.326C156.014 23.3336 156.029 23.3376 156.044 23.3376H158.683C158.766 23.3376 158.836 23.3898 158.895 23.493L159.586 24.7144C159.676 24.8746 159.7 24.9416 159.598 25.1122C159.474 25.3165 159.354 25.5228 159.236 25.73L159.062 26.0427C159.012 26.1359 158.956 26.1758 159.043 26.2861L160.303 28.4899C160.385 28.6329 160.356 28.7246 160.283 28.8558C160.075 29.2289 159.864 29.5991 159.648 29.9679C159.573 30.0972 159.481 30.1461 159.325 30.1438C158.956 30.1362 158.588 30.139 158.219 30.1514C158.211 30.1518 158.204 30.1542 158.197 30.1583C158.19 30.1625 158.185 30.1683 158.181 30.1751C157.756 30.9281 157.327 31.679 156.895 32.4279C156.815 32.5671 156.715 32.6004 156.551 32.6009C156.077 32.6023 155.599 32.6028 155.117 32.6018C155.072 32.6017 155.028 32.5897 154.989 32.5671C154.95 32.5445 154.918 32.512 154.896 32.473L154.261 31.369C154.258 31.3618 154.252 31.3557 154.245 31.3516C154.238 31.3475 154.23 31.3454 154.222 31.3457H151.789C151.654 31.36 151.527 31.3452 151.407 31.302L150.645 29.9855C150.622 29.9465 150.611 29.9023 150.61 29.8573C150.61 29.8123 150.622 29.768 150.644 29.7289L151.218 28.7213C151.226 28.7071 151.23 28.6909 151.23 28.6745C151.23 28.6581 151.226 28.6419 151.218 28.6277C150.919 28.1104 150.622 27.592 150.327 27.0726L149.951 26.4096C149.875 26.2623 149.869 26.1739 149.996 25.951C150.217 25.5646 150.437 25.1787 150.655 24.7933C150.718 24.6821 150.8 24.6346 150.933 24.6341C151.343 24.6323 151.753 24.6322 152.163 24.6336C152.174 24.6335 152.184 24.6307 152.193 24.6255C152.202 24.6202 152.209 24.6127 152.214 24.6037L153.548 22.2773C153.568 22.2419 153.597 22.2124 153.633 22.1919C153.668 22.1714 153.708 22.1605 153.748 22.1603C153.998 22.1599 154.249 22.1603 154.501 22.1575L154.984 22.1466C155.146 22.1451 155.328 22.1618 155.412 22.3082ZM153.781 22.4997C153.776 22.4997 153.771 22.501 153.767 22.5035C153.762 22.506 153.759 22.5096 153.756 22.5139L152.394 24.8974C152.387 24.9086 152.378 24.9179 152.367 24.9244C152.356 24.9309 152.343 24.9344 152.33 24.9344H150.968C150.941 24.9344 150.934 24.9463 150.948 24.9696L153.709 29.7964C153.721 29.8163 153.716 29.8258 153.693 29.8263L152.365 29.8334C152.346 29.8328 152.326 29.8376 152.31 29.8474C152.293 29.8571 152.279 29.8714 152.27 29.8886L151.643 30.9864C151.622 31.0235 151.633 31.0425 151.675 31.0425L154.391 31.0463C154.413 31.0463 154.43 31.0558 154.441 31.0753L155.108 32.2416C155.13 32.2801 155.151 32.2805 155.174 32.2416L157.553 28.0783L157.925 27.4215C157.927 27.4174 157.931 27.414 157.935 27.4117C157.939 27.4093 157.943 27.4081 157.948 27.4081C157.953 27.4081 157.957 27.4093 157.961 27.4117C157.965 27.414 157.968 27.4174 157.971 27.4215L158.647 28.6239C158.653 28.6329 158.66 28.6404 158.669 28.6455C158.678 28.6507 158.688 28.6534 158.698 28.6534L160.011 28.6438C160.015 28.6439 160.018 28.643 160.021 28.6413C160.024 28.6397 160.026 28.6373 160.028 28.6343C160.03 28.6314 160.031 28.6282 160.031 28.6248C160.031 28.6215 160.03 28.6182 160.028 28.6153L158.65 26.1982C158.645 26.1901 158.642 26.1808 158.642 26.1713C158.642 26.1618 158.645 26.1525 158.65 26.1445L158.789 25.9035L159.321 24.9639C159.333 24.9444 159.327 24.9344 159.305 24.9344H153.794C153.766 24.9344 153.759 24.9221 153.774 24.8978L154.455 23.7073C154.46 23.6992 154.463 23.6898 154.463 23.6802C154.463 23.6706 154.46 23.6613 154.455 23.6531L153.806 22.5144C153.803 22.5099 153.8 22.5062 153.795 22.5036C153.791 22.501 153.786 22.4996 153.781 22.4997ZM156.77 26.3113C156.792 26.3113 156.798 26.3208 156.786 26.3398L156.391 27.036L155.149 29.2151C155.147 29.2194 155.143 29.2229 155.139 29.2253C155.135 29.2277 155.13 29.229 155.125 29.2289C155.12 29.2289 155.116 29.2276 155.112 29.2252C155.107 29.2228 155.104 29.2193 155.102 29.2151L153.46 26.3483C153.451 26.3322 153.456 26.3236 153.474 26.3227L153.576 26.317L156.771 26.3113H156.77Z"
            fill="url(#paint21_linear_413_5957)" />
        </g>
      </g>
      <rect x="145.837" y="18.0871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter77_d_413_5957)">
      <g clip-path="url(#clip76_413_5957)">
        <rect x="18.25" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="18.25" y="36.5" width="18.25" height="18.25" fill="white" />
        <path
          d="M17.5983 45.6244C17.5983 40.2249 21.9755 35.8477 27.3751 35.8477C32.7746 35.8477 37.1518 40.2249 37.1518 45.6244C37.1518 51.024 32.7746 55.4012 27.3751 55.4012C21.9755 55.4012 17.5983 51.024 17.5983 45.6244Z"
          fill="white" fill-opacity="0.94" />
        <g clip-path="url(#clip77_413_5957)">
          <path d="M23.3009 41.5371H24.9306V43.1663H23.3009V41.5371ZM29.8186 41.5371H31.4488V43.1663H29.8186V41.5371Z"
            fill="#FFD700" />
          <path
            d="M23.3009 43.166H26.5598V44.7957H23.3014L23.3009 43.166ZM28.1894 43.166H31.4483V44.7957H28.1894V43.166Z"
            fill="#FFAF00" />
          <path d="M23.3009 44.7959H31.4488V46.4251H23.3009V44.7959Z" fill="#FF8205" />
          <path
            d="M23.3009 46.4268H24.9306V48.056H23.3009V46.4268ZM26.5602 46.4268H28.1899V48.056H26.5602V46.4268ZM29.8186 46.4268H31.4488V48.056H29.8186V46.4268Z"
            fill="#FA500F" />
          <path d="M21.6719 48.0547H26.5604V49.6844H21.6719V48.0547ZM28.1896 48.0547H33.0781V49.6844H28.1896V48.0547Z"
            fill="#E10500" />
        </g>
      </g>
      <rect x="18.0871" y="36.3371" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter78_d_413_5957)">
      <g clip-path="url(#clip78_413_5957)">
        <rect x="36.5" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" shape-rendering="crispEdges" />
        <rect x="36.5" y="18.25" width="18.25" height="18.25" fill="white" />
        <path
          d="M35.8483 27.7003C35.8483 22.1208 40.3714 17.5977 45.9509 17.5977C51.5305 17.5977 56.0536 22.1208 56.0536 27.7003C56.0536 33.2799 51.5305 37.803 45.9509 37.803C40.3714 37.803 35.8483 33.2799 35.8483 27.7003Z"
          fill="white" fill-opacity="0.94" />
        <g clip-path="url(#clip79_413_5957)">
          <path fill-rule="evenodd" clip-rule="evenodd"
            d="M46.1179 24.7302C46.0647 24.5491 46.0241 24.3645 45.9966 24.1777V23.6335C47.0124 23.6436 47.9872 24.0348 48.7282 24.7297L46.1179 24.7302ZM45.7791 24.7302C45.8346 24.547 45.8768 24.3594 45.9053 24.1679V23.6335C44.8896 23.6436 43.9147 24.0348 43.1738 24.7297H45.7786L45.7791 24.7302ZM43.1109 24.822H45.7496C45.7113 24.9379 45.6675 25.052 45.6185 25.1638C45.4158 25.6487 45.1224 26.0904 44.7542 26.4652L43.1109 24.822ZM42.9813 24.7302H41.5198V24.822H42.9513C42.2182 25.5838 41.8041 26.597 41.7938 27.6542H40.0577V27.7455H41.7938C41.8041 28.8027 42.2182 29.8159 42.9513 30.5777H41.5198V30.6691H42.9813V32.1311H43.0731V30.6995C43.8349 31.4326 44.8481 31.8467 45.9053 31.8571V33.5931H45.9966V31.8571C47.0538 31.8467 48.067 31.4326 48.8288 30.6995V32.1311H48.9202V30.6691H50.3822V30.5777H48.9506C49.6837 29.8159 50.0978 28.8027 50.1082 27.7455H51.8442V27.6542H50.1082C50.0978 26.597 49.6837 25.5838 48.9506 24.822H50.3822V24.7302H48.9202V23.2687H48.8288V24.7002C48.067 23.9671 47.0538 23.553 45.9966 23.5427V21.8066H45.9053V23.5427C44.8481 23.553 43.8349 23.9671 43.0731 24.7002V23.2687H42.9813V24.7302ZM45.9966 31.7657C47.0124 31.7555 47.9873 31.3641 48.7282 30.6691H46.1179C46.0649 30.8493 46.0246 31.034 45.9966 31.2221V31.7657ZM45.9053 31.2319C45.8769 31.0414 45.8345 30.8533 45.7786 30.6691H43.1738C43.9146 31.3641 44.8895 31.7555 45.9053 31.7657V31.2319ZM41.8851 27.6542H42.4288C42.6156 27.6267 42.8002 27.5862 42.9813 27.5329V24.9227C42.2864 25.6636 41.8952 26.6385 41.8851 27.6542ZM42.419 27.7455H41.8846C41.8947 28.7613 42.2859 29.7361 42.9808 30.4771V27.8722C42.7967 27.8163 42.6092 27.774 42.419 27.7455ZM50.0163 27.6542C50.0063 26.6385 49.615 25.6636 48.9202 24.9227V27.5329C49.1004 27.5859 49.2851 27.6262 49.4732 27.6542H50.0163ZM49.483 27.7455C49.2914 27.774 49.1038 27.8163 48.9202 27.8722V30.4771C49.6152 29.7362 50.0066 28.7613 50.0168 27.7455H49.483ZM48.8288 30.5399V27.9012C48.7129 27.9396 48.5988 27.9833 48.487 28.0323C48.0022 28.235 47.5605 28.5284 47.1856 28.8967L48.8288 30.5399ZM48.8288 27.5049V24.8284L48.8229 24.822H46.1459C46.1833 24.9374 46.2255 25.0518 46.2736 25.1638C46.4859 25.656 46.789 26.1038 47.1669 26.4839C47.547 26.862 47.9948 27.1652 48.487 27.3777C48.599 27.4253 48.7129 27.4676 48.8288 27.5049ZM43.0731 30.5718V27.9012C43.1885 27.9395 43.3029 27.9832 43.4149 28.0323C43.908 28.2386 44.3563 28.5386 44.735 28.9158C45.1122 29.2945 45.4122 29.7428 45.6185 30.2359C45.6676 30.3479 45.7118 30.4618 45.7496 30.5777H43.079L43.0731 30.5718ZM43.0731 27.5049V24.9134L44.6888 26.5291C44.3186 26.8862 43.8871 27.1737 43.4149 27.3777C43.303 27.4253 43.1889 27.4677 43.0731 27.5049ZM46.1459 30.5777H48.7375L47.1218 28.962C46.7646 29.3322 46.4772 29.7637 46.2731 30.2359C46.2256 30.3479 46.1831 30.4619 46.1459 30.5777ZM44.9933 26.7422C44.5857 27.1481 44.1061 27.4745 43.5789 27.7048C44.107 27.9291 44.5871 28.2525 44.9933 28.6575C45.3984 29.0638 45.7218 29.5439 45.9461 30.0719C46.1763 29.5448 46.5028 29.0651 46.9086 28.6575C47.3149 28.2525 47.795 27.9291 48.323 27.7048C47.7959 27.4745 47.3162 27.1481 46.9086 26.7422C46.5027 26.3346 46.1763 25.855 45.9461 25.3278C45.7218 25.8559 45.3984 26.336 44.9933 26.7422Z"
            fill="url(#paint22_linear_413_5957)" />
        </g>
      </g>
      <rect x="36.3371" y="18.0871" width="18.5759" height="18.5759" rx="0.814732" stroke="black" stroke-opacity="0.2"
        stroke-width="0.325893" shape-rendering="crispEdges" />
    </g>
  </g>
  <defs>
    <filter id="filter0_d_413_5957" x="235.621" y="-1.62974" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter1_d_413_5957" x="235.621" y="-19.8797" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter2_d_413_5957" x="235.621" y="16.6203" width="21.509" height="21.5095" filterUnits="userSpaceOnUse"
      color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter3_d_413_5957" x="217.371" y="-1.62974" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter4_d_413_5957" x="217.371" y="-19.8797" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter5_d_413_5957" x="199.121" y="-1.62974" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter6_d_413_5957" x="199.121" y="-19.8797" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter7_d_413_5957" x="199.121" y="16.6203" width="21.509" height="21.5095" filterUnits="userSpaceOnUse"
      color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter8_d_413_5957" x="180.621" y="-1.37974" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter9_d_413_5957" x="186.11" y="2.8" width="10.781" height="13.4" filterUnits="userSpaceOnUse"
      color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.6" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.11 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter10_ddi_413_5957" x="187.321" y="-1.23339" width="13.7881" height="13.7881"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset dx="-0.0810871" dy="0.0810871" />
      <feGaussianBlur stdDeviation="0.121631" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="2.43261" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0" />
      <feBlend mode="normal" in2="effect1_dropShadow_413_5957" result="effect2_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_413_5957" result="shape" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset dy="-0.0405435" />
      <feGaussianBlur stdDeviation="0.142166" />
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0" />
      <feBlend mode="normal" in2="shape" result="effect3_innerShadow_413_5957" />
    </filter>
    <filter id="filter11_d_413_5957" x="180.871" y="-19.8797" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter12_d_413_5957" x="180.871" y="16.6203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter13_d_413_5957" x="162.621" y="-1.62974" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter14_d_413_5957" x="162.621" y="-19.8797" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter15_d_413_5957" x="162.621" y="16.6203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter16_d_413_5957" x="144.371" y="-1.62974" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter17_d_413_5957" x="144.371" y="-19.8797" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter18_d_413_5957" x="126.621" y="-1.37974" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter19_d_413_5957" x="132.11" y="2.8" width="10.781" height="13.4" filterUnits="userSpaceOnUse"
      color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.6" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.11 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter20_ddi_413_5957" x="133.321" y="-1.23339" width="13.7881" height="13.7881"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset dx="-0.0810871" dy="0.0810871" />
      <feGaussianBlur stdDeviation="0.121631" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="2.43261" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0" />
      <feBlend mode="normal" in2="effect1_dropShadow_413_5957" result="effect2_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_413_5957" result="shape" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset dy="-0.0405435" />
      <feGaussianBlur stdDeviation="0.142166" />
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0" />
      <feBlend mode="normal" in2="shape" result="effect3_innerShadow_413_5957" />
    </filter>
    <filter id="filter21_d_413_5957" x="126.121" y="-19.8797" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter22_d_413_5957" x="126.121" y="16.6203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter23_d_413_5957" x="107.871" y="-1.62974" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter24_d_413_5957" x="107.871" y="-19.8797" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter25_d_413_5957" x="107.871" y="16.6203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter26_d_413_5957" x="89.6205" y="-1.62974" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter27_d_413_5957" x="89.6205" y="-19.8797" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter28_d_413_5957" x="89.6205" y="16.6203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter29_d_413_5957" x="95.1096" y="20.8" width="10.781" height="13.4" filterUnits="userSpaceOnUse"
      color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.6" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.11 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter30_ddi_413_5957" x="96.3208" y="16.7666" width="13.7881" height="13.7881"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset dx="-0.0810871" dy="0.0810871" />
      <feGaussianBlur stdDeviation="0.121631" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="2.43261" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0" />
      <feBlend mode="normal" in2="effect1_dropShadow_413_5957" result="effect2_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_413_5957" result="shape" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset dy="-0.0405435" />
      <feGaussianBlur stdDeviation="0.142166" />
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0" />
      <feBlend mode="normal" in2="shape" result="effect3_innerShadow_413_5957" />
    </filter>
    <filter id="filter31_d_413_5957" x="71.3705" y="-19.8797" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter32_d_413_5957" x="71.3705" y="16.6203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter33_d_413_5957" x="53.1205" y="-1.62974" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter34_d_413_5957" x="53.1205" y="-19.8797" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter35_d_413_5957" x="53.1205" y="16.6203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter36_d_413_5957" x="34.8705" y="-1.62974" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter37_d_413_5957" x="34.8705" y="-19.8797" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter38_d_413_5957" x="235.621" y="34.8703" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter39_d_413_5957" x="235.621" y="53.1203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter40_d_413_5957" x="217.371" y="34.8703" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter41_d_413_5957" x="217.371" y="53.1203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter42_d_413_5957" x="199.621" y="34.6203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter43_d_413_5957" x="205.11" y="38.8" width="10.781" height="13.4" filterUnits="userSpaceOnUse"
      color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.6" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.11 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter44_ddi_413_5957" x="206.321" y="34.7666" width="13.7881" height="13.7881"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset dx="-0.0810871" dy="0.0810871" />
      <feGaussianBlur stdDeviation="0.121631" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="2.43261" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0" />
      <feBlend mode="normal" in2="effect1_dropShadow_413_5957" result="effect2_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_413_5957" result="shape" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset dy="-0.0405435" />
      <feGaussianBlur stdDeviation="0.142166" />
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0" />
      <feBlend mode="normal" in2="shape" result="effect3_innerShadow_413_5957" />
    </filter>
    <filter id="filter45_d_413_5957" x="199.121" y="53.1203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter46_d_413_5957" x="180.871" y="34.8703" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter47_d_413_5957" x="180.871" y="53.1203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter48_d_413_5957" x="162.621" y="34.8703" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter49_d_413_5957" x="162.621" y="53.1203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter50_d_413_5957" x="144.371" y="34.8703" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter51_d_413_5957" x="144.371" y="53.1203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter52_d_413_5957" x="126.121" y="34.8703" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter53_d_413_5957" x="126.121" y="53.1203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter54_d_413_5957" x="107.871" y="34.8703" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter55_d_413_5957" x="107.871" y="53.1203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter56_d_413_5957" x="89.6205" y="34.8703" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter57_d_413_5957" x="89.6205" y="53.1203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter58_d_413_5957" x="71.3705" y="34.8703" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter59_d_413_5957" x="71.3705" y="53.1203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter60_d_413_5957" x="53.1205" y="34.8703" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter61_d_413_5957" x="53.1205" y="53.1203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter62_d_413_5957" x="34.8705" y="34.8703" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter63_d_413_5957" x="34.8705" y="53.1203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter64_d_413_5957" x="16.6205" y="-1.62974" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter65_d_413_5957" x="16.6205" y="-19.8797" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter66_d_413_5957" x="16.6205" y="16.6203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter67_d_413_5957" x="-1.6295" y="-1.62974" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter68_d_413_5957" x="-1.6295" y="-19.8797" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter69_d_413_5957" x="-1.6295" y="16.6203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter70_d_413_5957" x="16.6205" y="53.1203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter71_d_413_5957" x="-1.6295" y="34.8703" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter72_d_413_5957" x="-1.6295" y="53.1203" width="21.509" height="21.5095"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="0.651786" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter73_d_413_5957" x="36.9526" y="31.7234" width="181.832" height="136.994"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset dy="1.94008" />
      <feGaussianBlur stdDeviation="5.96574" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter74_d_413_5957" x="211.961" y="11.2104" width="32.3286" height="32.3291"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="3.3567" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0.141176 0 0 0 0 0.72549 0 0 0 0 0.376471 0 0 0 0.3 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter75_d_413_5957" x="65.9607" y="-7.03957" width="32.3286" height="32.3291"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="3.3567" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0.141176 0 0 0 0 0.72549 0 0 0 0 0.376471 0 0 0 0.3 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter76_d_413_5957" x="138.961" y="11.2104" width="32.3286" height="32.3291"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="3.3567" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0.141176 0 0 0 0 0.72549 0 0 0 0 0.376471 0 0 0 0.3 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter77_d_413_5957" x="11.2107" y="29.4604" width="32.3286" height="32.3291"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="3.3567" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0.141176 0 0 0 0 0.72549 0 0 0 0 0.376471 0 0 0 0.3 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <filter id="filter78_d_413_5957" x="29.4607" y="11.2104" width="32.3286" height="32.3291"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="3.3567" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0.141176 0 0 0 0 0.72549 0 0 0 0 0.376471 0 0 0 0.3 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_413_5957" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_413_5957" result="shape" />
    </filter>
    <linearGradient id="paint0_linear_413_5957" x1="183.333" y1="4" x2="189.235" y2="15.8039"
      gradientUnits="userSpaceOnUse">
      <stop stop-color="#EBEBEB" />
      <stop offset="1" stop-color="#FAFAFB" />
    </linearGradient>
    <linearGradient id="paint1_linear_413_5957" x1="194.584" y1="5.75286" x2="193.2" y2="7.13613"
      gradientUnits="userSpaceOnUse">
      <stop stop-color="#EEEEEE" />
      <stop offset="1" stop-color="white" />
    </linearGradient>
    <linearGradient id="paint2_linear_413_5957" x1="129.333" y1="4" x2="135.235" y2="15.8039"
      gradientUnits="userSpaceOnUse">
      <stop stop-color="#EBEBEB" />
      <stop offset="1" stop-color="#FAFAFB" />
    </linearGradient>
    <linearGradient id="paint3_linear_413_5957" x1="140.584" y1="5.75286" x2="139.2" y2="7.13613"
      gradientUnits="userSpaceOnUse">
      <stop stop-color="#EEEEEE" />
      <stop offset="1" stop-color="white" />
    </linearGradient>
    <linearGradient id="paint4_linear_413_5957" x1="92.3331" y1="22" x2="98.235" y2="33.8039"
      gradientUnits="userSpaceOnUse">
      <stop stop-color="#EBEBEB" />
      <stop offset="1" stop-color="#FAFAFB" />
    </linearGradient>
    <linearGradient id="paint5_linear_413_5957" x1="103.584" y1="23.7529" x2="102.2" y2="25.1361"
      gradientUnits="userSpaceOnUse">
      <stop stop-color="#EEEEEE" />
      <stop offset="1" stop-color="white" />
    </linearGradient>
    <linearGradient id="paint6_linear_413_5957" x1="202.333" y1="40" x2="208.235" y2="51.8039"
      gradientUnits="userSpaceOnUse">
      <stop stop-color="#EBEBEB" />
      <stop offset="1" stop-color="#FAFAFB" />
    </linearGradient>
    <linearGradient id="paint7_linear_413_5957" x1="213.584" y1="41.7529" x2="212.2" y2="43.1361"
      gradientUnits="userSpaceOnUse">
      <stop stop-color="#EEEEEE" />
      <stop offset="1" stop-color="white" />
    </linearGradient>
    <linearGradient id="paint8_linear_413_5957" x1="505.908" y1="412.654" x2="276.944" y2="111.664"
      gradientUnits="userSpaceOnUse">
      <stop offset="0.0006" stop-color="#0867DF" />
      <stop offset="0.4539" stop-color="#0668E1" />
      <stop offset="0.8591" stop-color="#0064E0" />
    </linearGradient>
    <linearGradient id="paint9_linear_413_5957" x1="273.697" y1="182.886" x2="437.067" y2="58.5446"
      gradientUnits="userSpaceOnUse">
      <stop offset="0.1323" stop-color="#0064DF" />
      <stop offset="0.9988" stop-color="#0064E0" />
    </linearGradient>
    <linearGradient id="paint10_linear_413_5957" x1="295.38" y1="241.784" x2="361.983" y2="73.6167"
      gradientUnits="userSpaceOnUse">
      <stop offset="0.0147" stop-color="#0072EC" />
      <stop offset="0.6881" stop-color="#0064DF" />
    </linearGradient>
    <linearGradient id="paint11_linear_413_5957" x1="285.187" y1="158.549" x2="293.312" y2="50.2398"
      gradientUnits="userSpaceOnUse">
      <stop offset="0.0731" stop-color="#007CF6" />
      <stop offset="0.9943" stop-color="#0072EC" />
    </linearGradient>
    <linearGradient id="paint12_linear_413_5957" x1="288.879" y1="74.9857" x2="286.544" y2="57.5556"
      gradientUnits="userSpaceOnUse">
      <stop offset="0.0731" stop-color="#007FF9" />
      <stop offset="1" stop-color="#007CF6" />
    </linearGradient>
    <linearGradient id="paint13_linear_413_5957" x1="278.769" y1="47.5363" x2="314.412" y2="123.38"
      gradientUnits="userSpaceOnUse">
      <stop offset="0.0731" stop-color="#007FF9" />
      <stop offset="1" stop-color="#0082FB" />
    </linearGradient>
    <linearGradient id="paint14_linear_413_5957" x1="270.596" y1="124.736" x2="313.58" y2="65.1123"
      gradientUnits="userSpaceOnUse">
      <stop offset="0.2799" stop-color="#007FF8" />
      <stop offset="0.9141" stop-color="#0082FB" />
    </linearGradient>
    <linearGradient id="paint15_linear_413_5957" x1="328.695" y1="49.3788" x2="424.17" y2="401.713"
      gradientUnits="userSpaceOnUse">
      <stop stop-color="#0082FB" />
      <stop offset="0.9995" stop-color="#0081FA" />
    </linearGradient>
    <linearGradient id="paint16_linear_413_5957" x1="308.681" y1="37.6886" x2="252.668" y2="151.254"
      gradientUnits="userSpaceOnUse">
      <stop offset="0.0619" stop-color="#0081FA" />
      <stop offset="1" stop-color="#0080F9" />
    </linearGradient>
    <linearGradient id="paint17_linear_413_5957" x1="270.09" y1="121.683" x2="315.286" y2="90.8037"
      gradientUnits="userSpaceOnUse">
      <stop stop-color="#027AF3" />
      <stop offset="1" stop-color="#0080F9" />
    </linearGradient>
    <linearGradient id="paint18_linear_413_5957" x1="258.476" y1="95.0771" x2="340.906" y2="95.0771"
      gradientUnits="userSpaceOnUse">
      <stop stop-color="#0377EF" />
      <stop offset="0.9994" stop-color="#0279F1" />
    </linearGradient>
    <linearGradient id="paint19_linear_413_5957" x1="282.925" y1="84.3426" x2="329.335" y2="111.74"
      gradientUnits="userSpaceOnUse">
      <stop offset="0.0019" stop-color="#0471E9" />
      <stop offset="1" stop-color="#0377EF" />
    </linearGradient>
    <linearGradient id="paint20_linear_413_5957" x1="310.509" y1="99.1795" x2="462.105" y2="290.365"
      gradientUnits="userSpaceOnUse">
      <stop offset="0.2765" stop-color="#0867DF" />
      <stop offset="1" stop-color="#0471E9" />
    </linearGradient>
    <linearGradient id="paint21_linear_413_5957" x1="149.897" y1="22.1465" x2="1195.48" y2="22.1465"
      gradientUnits="userSpaceOnUse">
      <stop stop-color="#00055F" stop-opacity="0.84" />
      <stop offset="1" stop-color="#6F69F7" stop-opacity="0.84" />
    </linearGradient>
    <linearGradient id="paint22_linear_413_5957" x1="327.871" y1="912.639" x2="926.329" y2="318.755"
      gradientUnits="userSpaceOnUse">
      <stop stop-color="#446EFF" />
      <stop offset="0.36661" stop-color="#2E96FF" />
      <stop offset="0.83221" stop-color="#B1C5FF" />
    </linearGradient>
    <clipPath id="clip0_413_5957">
      <path d="M0 0H256.152V62.5714C256.152 68.331 251.483 73 245.723 73H10.4286C4.66905 73 0 68.331 0 62.5714V0Z"
        fill="white" />
    </clipPath>
    <clipPath id="clip1_413_5957">
      <rect x="237.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip2_413_5957">
      <rect x="237.25" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip3_413_5957">
      <rect x="237.25" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip4_413_5957">
      <rect x="219" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip5_413_5957">
      <rect x="219" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip6_413_5957">
      <rect x="200.75" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip7_413_5957">
      <rect x="200.75" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip8_413_5957">
      <rect x="200.75" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip9_413_5957">
      <rect x="182.25" y="0.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip10_413_5957">
      <rect width="8.38095" height="11" fill="white" transform="translate(187.31 4)" />
    </clipPath>
    <clipPath id="clip11_413_5957">
      <rect x="182.5" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip12_413_5957">
      <rect x="182.5" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip13_413_5957">
      <rect x="164.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip14_413_5957">
      <rect x="164.25" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip15_413_5957">
      <rect x="164.25" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip16_413_5957">
      <rect x="146" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip17_413_5957">
      <rect x="146" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip18_413_5957">
      <rect x="128.25" y="0.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip19_413_5957">
      <rect width="8.38095" height="11" fill="white" transform="translate(133.31 4)" />
    </clipPath>
    <clipPath id="clip20_413_5957">
      <rect x="127.75" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip21_413_5957">
      <rect x="127.75" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip22_413_5957">
      <rect x="109.5" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip23_413_5957">
      <rect x="109.5" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip24_413_5957">
      <rect x="109.5" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip25_413_5957">
      <rect x="91.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip26_413_5957">
      <rect x="91.25" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip27_413_5957">
      <rect x="91.25" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip28_413_5957">
      <rect width="8.38095" height="11" fill="white" transform="translate(96.3096 22)" />
    </clipPath>
    <clipPath id="clip29_413_5957">
      <rect x="73" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip30_413_5957">
      <rect x="73" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip31_413_5957">
      <rect x="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip32_413_5957">
      <rect x="54.75" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip33_413_5957">
      <rect x="54.75" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip34_413_5957">
      <rect x="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip35_413_5957">
      <rect x="36.5" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip36_413_5957">
      <rect x="237.25" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip37_413_5957">
      <rect x="237.25" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip38_413_5957">
      <rect x="219" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip39_413_5957">
      <rect x="219" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip40_413_5957">
      <rect x="201.25" y="36.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip41_413_5957">
      <rect width="8.38095" height="11" fill="white" transform="translate(206.31 40)" />
    </clipPath>
    <clipPath id="clip42_413_5957">
      <rect x="200.75" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip43_413_5957">
      <rect x="182.5" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip44_413_5957">
      <rect x="182.5" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip45_413_5957">
      <rect x="164.25" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip46_413_5957">
      <rect x="164.25" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip47_413_5957">
      <rect x="146" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip48_413_5957">
      <rect x="146" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip49_413_5957">
      <rect x="127.75" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip50_413_5957">
      <rect x="127.75" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip51_413_5957">
      <rect x="109.5" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip52_413_5957">
      <rect x="109.5" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip53_413_5957">
      <rect x="91.25" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip54_413_5957">
      <rect x="91.25" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip55_413_5957">
      <rect x="73" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip56_413_5957">
      <rect x="73" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip57_413_5957">
      <rect x="54.75" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip58_413_5957">
      <rect x="54.75" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip59_413_5957">
      <rect x="36.5" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip60_413_5957">
      <rect x="36.5" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip61_413_5957">
      <rect x="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip62_413_5957">
      <rect x="18.25" y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip63_413_5957">
      <rect x="18.25" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip64_413_5957">
      <rect width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip65_413_5957">
      <rect y="-18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip66_413_5957">
      <rect y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip67_413_5957">
      <rect x="18.25" y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip68_413_5957">
      <rect y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip69_413_5957">
      <rect y="54.75" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip70_413_5957">
      <rect x="219" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip71_413_5957">
      <rect width="11.4062" height="11.4062" fill="white" transform="translate(222.422 21.6709)" />
    </clipPath>
    <clipPath id="clip72_413_5957">
      <rect x="73" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip73_413_5957">
      <rect width="11.4062" height="11.4062" fill="white" transform="translate(76.4219 3.4209)" />
    </clipPath>
    <clipPath id="clip74_413_5957">
      <rect x="146" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip75_413_5957">
      <rect width="11.4062" height="11.4062" fill="white" transform="translate(149.422 21.6709)" />
    </clipPath>
    <clipPath id="clip76_413_5957">
      <rect x="18.25" y="36.5" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip77_413_5957">
      <rect width="11.4062" height="11.4062" fill="white" transform="translate(21.6719 39.9209)" />
    </clipPath>
    <clipPath id="clip78_413_5957">
      <rect x="36.5" y="18.25" width="18.25" height="18.25" rx="0.651786" fill="white" />
    </clipPath>
    <clipPath id="clip79_413_5957">
      <rect width="11.7865" height="11.7865" fill="white" transform="translate(40.0577 21.8066)" />
    </clipPath>
  </defs>
</svg>